<tasks>
  <phase id="setup">
    <t id="setup-structure">创建 frontend-vue 源码目录与 Vite 工程，配置 base=/app-v2 与 build.outDir=../frontend/public/app-v2。</t>
    <t id="setup-routing">建立 Vue Router（/sign-in、/devices、/devices/:id 等），守卫登录态。</t>
    <t id="setup-state">初始化 Pinia 与类型（Node、Point、Response）。</t>
    <t id="setup-axios">封装 Axios（baseURL、拦截器、401 处理、错误格式化）。</t>
  </phase>

  <phase id="auth">
    <t id="auth-page">实现登录页：POST /v1/auth，保存 token。</t>
    <t id="auth-guard">路由守卫：无 token 时跳转登录，有 token 注入 Authorization。</t>
  </phase>

  <phase id="core">
    <t id="devices-list">设备列表：分页/搜索/筛选/标签，渲染 Node 简要信息。</t>
    <t id="devices-detail">设备详情：点位表格读写，提交 /v1/nodes/:id/points。</t>
    <t id="devices-mutate">移动/复制/删除：调用 /parents POST/PUT 与 DELETE。</t>
    <t id="updates">软件更新：任务列表与详情、进度与错误，实时/轮询。</t>
    <t id="files">文件：上传、分发到设备/分组。</t>
  </phase>

  <phase id="realtime">
    <t id="nats-client">封装 NATS WS 客户端；订阅设备/点位主题。</t>
    <t id="reconnect">断线重连与全量校准（GET /v1/nodes）。</t>
    <t id="polling">实现 4s 轮询兜底与合并策略。</t>
  </phase>

  <phase id="modules">
    <t id="groups">Groups 视图：扁平/分层切换。</t>
    <t id="rules">Rules 首版：表单式配置与列表。</t>
    <t id="metrics">Metrics 首版：实时曲线与卡片。</t>
    <t id="updates">Updates：任务列表与进度。</t>
    <t id="files">Files：上传/分发。</t>
    <t id="users">Users：用户与角色管理。</t>
    <t id="settings">Settings：系统参数与调试工具。</t>
  </phase>

  <phase id="integration">
    <t id="vite-proxy">Vite 开发代理 /v1 与 WS。</t>
    <t id="embed-verify">验证 Go 服务静态资源可访问 /app-v2。</t>
    <t id="switch-entry">旧 UI 顶部加入“切换到新版 UI”入口（可选）。</t>
  </phase>

  <phase id="qa">
    <t id="tests">编写单元与 e2e 测试：认证、列表、详情、点位提交。</t>
    <t id="perf">虚拟滚动与大列表性能优化。</t>
    <t id="docs">补充用户手册与开发文档。</t>
  </phase>
</tasks>

