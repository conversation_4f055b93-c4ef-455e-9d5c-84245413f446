<api-map>
  <auth>
    <login method="POST" url="/v1/auth">
      <request>multipart/form-data: email, password</request>
      <response>{ token, email }</response>
      <notes>参见 frontend/src/Api/Auth.elm 与 Pages/SignIn.elm</notes>
    </login>
  </auth>

  <nodes base="/v1/nodes">
    <list method="GET" url="/v1/nodes">
      <auth>Authorization: Bearer &lt;token&gt;</auth>
      <response>List&lt;Node&gt;</response>
      <source>frontend/src/Api/Node.elm:list()</source>
    </list>

    <insert method="POST" url="/v1/nodes/:id">
      <auth>Authorization: Bearer &lt;token&gt;</auth>
      <body>Node (JSON)</body>
      <response>Standard Response</response>
      <source>frontend/src/Api/Node.elm:insert()</source>
    </insert>

    <delete method="DELETE" url="/v1/nodes/:id">
      <auth>Authorization: Bearer &lt;token&gt;</auth>
      <body>{ parent }</body>
      <response>Standard Response</response>
      <source>frontend/src/Api/Node.elm:delete()</source>
    </delete>

    <post-points method="POST" url="/v1/nodes/:id/points">
      <auth>Authorization: Bearer &lt;token&gt;</auth>
      <body>List&lt;Point&gt;</body>
      <response>Standard Response</response>
      <source>frontend/src/Api/Node.elm:postPoints()</source>
    </post-points>

    <notify method="POST" url="/v1/nodes/:id/not">
      <auth>Authorization: Bearer &lt;token&gt;</auth>
      <body>Notification</body>
      <response>Standard Response</response>
      <source>frontend/src/Api/Node.elm:notify()</source>
    </notify>

    <move method="POST" url="/v1/nodes/:id/parents">
      <auth>Authorization: Bearer &lt;token&gt;</auth>
      <body>{ id, oldParent, newParent }</body>
      <response>Standard Response</response>
      <source>frontend/src/Api/Node.elm:move()</source>
    </move>

    <copy method="PUT" url="/v1/nodes/:id/parents">
      <auth>Authorization: Bearer &lt;token&gt;</auth>
      <body>{ id, newParent, duplicate }</body>
      <response>Standard Response</response>
      <source>frontend/src/Api/Node.elm:copy()</source>
    </copy>
  </nodes>

  <static-assets>
    <served-by>api/server.go -> http.FileServer(args.Filesystem)</served-by>
    <embed>server/server.go: fs.Sub(frontend.Content, "public") 或 Dev 模式 ./frontend/public</embed>
    <entry-html>frontend/public/index.html（旧 Elm）; 新版建议 /app-v2/index.html 由 Vite 生成</entry-html>
    <legacy-bundle>frontend/public/dist/elm.js（gzip 版本由 envsetup.sh 生成）</legacy-bundle>
    <ws-proxy>api/server.go: 当 NatsWSPort &gt; 0 时，根路径 " / " 的 websocket 升级由 websocketproxy 转发到 nats ws 端口</ws-proxy>
  </static-assets>
</api-map>

