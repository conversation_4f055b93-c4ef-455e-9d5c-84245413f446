syntax = "proto3";
package pb;

option go_package = "internal/pb";

import "google/protobuf/timestamp.proto";

message Point {
  string type = 2;
  double value = 4;
  google.protobuf.Timestamp time = 5;
  string text = 8;
  string key = 11;
  int32 tombstone = 12;
  bytes data = 14;
  string origin = 15;
}

message Points { repeated Point points = 1; }

message SerialPoint {
  string type = 2;
  float value = 4;
  int64 time = 16;
  string text = 8;
  string key = 11;
  int32 tombstone = 12;
  bytes data = 14;
  string origin = 15;
}

message SerialPoints { repeated SerialPoint points = 1; }

message PointArray {
  uint64 starttime = 1;
  string type = 2;
  string key = 3;
  float samplerate = 4;
  repeated float values = 5;
}
