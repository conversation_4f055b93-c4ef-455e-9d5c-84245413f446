#!/bin/sh
#
PIDFILE=/var/run/siot.pid
DESC="Simple IoT cloud/edge application/framework portal"
DATADIR=/data/siot

check_dbs () {
  echo "Add Database checks"
}

mkdir -p $DATADIR

case "$1" in
  start)
        echo -n "Starting $DESC"
        check_dbs
        start-stop-daemon -S -m -p $PIDFILE -b -x /usr/bin/env SIOT_PORT=80 /usr/bin/siot -- -syslog
        ;;
  stop)
        echo -n "Stopping $DESC"
        start-stop-daemon -K -p $PIDFILE --retry 5
        ;;
  restart)
  echo -n "Restarting $DESC"
        start-stop-daemon -K -p $PIDFILE --retry 5
        start-stop-daemon -S -m -p $PIDFILE -b -x /usr/bin/env SIOT_PORT=80 /usr/bin/siot -- -syslog
        ;;
  *)
        echo "Usage: $N {start|stop|restart}" >&2
        exit 1
        ;;
esac

exit 0
