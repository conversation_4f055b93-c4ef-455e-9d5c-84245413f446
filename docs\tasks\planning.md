

## 任务接收与思路说明
- 目标：基于对现有 Elm 前端与后端 API 的深入理解，给出一份面向最终用户的 Vue 3 + TypeScript 重构方案与详细开发计划（不写代码，仅分析与规划）。
- 方法：已快速梳理了仓库结构、核心数据模型（Node/Point/NodeEdge）、HTTP API（/v1/nodes, /v1/auth 等）、现有 Elm 前端的交互模式（树形浏览器 + 4s 轮询）与 NATS over WebSocket 的实时能力。下面从“核心功能解读 → UX 与信息架构 → 技术架构 → 数据与权限模型 → 页面规划与交互 → 实时同步 → 迁移策略 → 里程碑与风险”逐步展开。

## 核心功能解读（现状）
- 数据域模型
  - NodeEdge：系统的“实体 + 关系”载体，包含 id、type、parent、points、edgePoints（Go: data.NodeEdge）。
  - Point：通用键值数据结构（type、key、value/text、time、origin、tombstone），既可表示传感数据，也可表示配置参数（Go: data.Point）。
  - NodeType/PointType：用常量定义核心节点/点类型（user、jwt、device、group、rule、modbus、networkManager、update、file、metrics、message 等）。
- 后端能力
  - HTTP API v1（api）：/v1/nodes（GET 列表、POST 新建），/v1/nodes/:id（GET 详情、DELETE 删除），/v1/nodes/:id/parents（POST move、PUT copy），/v1/nodes/:id/points（POST 点位更新），/v1/auth（POST 登录获取 token）。
  - 权限：NodesHandler 对请求做鉴权（Authorization 头部校验；支持 authToken 旁路或 JWT 验证），GetNodesForUser 根据用户过滤可见节点。
  - 静态资源：Go HTTP 服务器内置前端静态资源（当前 Elm）。
  - 实时：NATS over WebSocket 可用于前端实时订阅；现 Elm 前端同时有 4s 轮询 tick 兜底。
- 现有 UI 的特点与痛点
  - Elm 单页应用，以工程师视角的“树形结构 NodeBrowser”为主要导航方式；对最终用户不够直观（难找内容、任务导向弱）。
  - 各 NodeType 拥有专用组件（NodeDevice/NodeModbus/NodeMetrics/...），专业性强但学习曲线陡。
  - 状态更新基于定时轮询 + 端口交互；有实时能力但使用门槛较高。

## 重构目标与总体设计原则
- 用户导向：告别树形主导航，转为“任务导向 + 扁平化列表 + 搜索/筛选 + 标签”的信息架构；保留树结构为“专家模式/次要视图”。
- 渐进式：新旧前端并行（例如 /app-v2 路由前缀），灰度迁移；避免一次性切换造成风险。
- 即插即用：充分复用现有 API 与 NATS 能力，尽量避免后端大改。
- 可维护性：Vue 3 + TS + 组合式 API，清晰的实体建模、统一的数据访问层、可测试性好。
- 性能与可用性：大列表虚拟滚动、去抖与批量合并更新、错误可视化、断线重连策略。

## 信息架构（IA）与导航
- 顶部栏（全局）
  - 全局搜索（按名称/描述/类型/标签过滤）
  - 用户头像与账号菜单（个人信息、退出登录、切换到旧版 UI）
  - 系统状态（可选：NATS 连接状态指示、告警提示）
- 侧边菜单（按角色动态显示）
  - 概览 Dashboard（快捷卡片：在线设备、告警、最近更新、消息）
  - 设备 Devices（默认页，扁平列表 + 搜索/筛选/标签）
  - 分组 Groups（以任务为导向的集合视图；树形仅作为二级视图）
  - 协议与连接 Protocols & Networks
    - Modbus、CANBus、NetworkManager、OneWire、Shelly、Particle 等
  - 规则/自动化 Rules（条件与动作的配置）
  - 指标与监控 Metrics（实时/近实时视图，趋势/图表）
  - 软件更新 Updates（进度、错误、百分比，批量下发）
  - 文件管理 Files（上传/分发/版本）
  - 消息中心 Messages/Notifications（推送记录、订阅/偏好）
  - 用户与角色 Users & Roles（Admin 专属）
  - 设置 Settings（系统/客户端/调试）
- 二级与详情导航
  - 设备详情页 Tabs：概览｜点位｜命令/参数｜历史（可选）｜日志（可选）｜关联（分组/规则）
  - 各专业模块（如 Modbus）有定制页面，但优先统一操作模式与语言（减少“工程型”门槛）

## 技术架构（Vue 3 + TS）
- 基础栈
  - 构建：Vite + Vue 3 + TypeScript
  - 路由：Vue Router（守卫控制登录态与权限）
  - 状态：Pinia（实体归一化存储：NodeEdge、Point 与索引）
  - UI 组件库：建议 Element Plus 或 Naive UI（成熟、稳定、表格/表单/对话框丰富）
  - 图表：ECharts（折线、仪表、热力，适合工业场景）
  - 表格与虚拟滚动：Element Plus Table + 虚拟滚动插件 或 Vue Virtual Scroller
  - HTTP：Axios（拦截器注入 Authorization、统一错误处理、重试/取消）
  - 实时：nats.ws（NATS over WS）优先；不可用时 fallback 轮询（4s，与现有一致）
- 模块化设计
  - api 层：/v1/auth、/v1/nodes、/v1/nodes/:id/points、/parents 等封装；统一响应类型与错误处理
  - domain 层：TS 类型定义与转换（NodeEdge、Point、Role、SwUpdateState 等）
  - stores 层（Pinia）：实体仓库、选择器（按 type/标签/搜索词）、派发合并更新
  - views/components：按业务域与节点类型分包；节点类型组件通过注册表动态路由/渲染
  - services 层：NATS 客户端封装（连接、订阅主题、去抖处理、重连策略）

## 数据与权限模型（前端角度）
- 认证与状态
  - 登录：POST /v1/auth，返回 token 与 email；token 持久化（localStorage/secure storage）并注入 Authorization 头（建议 Bearer <token>）。
  - 续期与登出：在 401 时自动登出并跳转登录；支持手动登出。
- 数据可见性
  - 后端基于用户过滤节点（GetNodesForUser）；前端只展示返回的节点集合。
- 角色与能力
  - User 节点与 role 点位（admin/user）决定菜单与操作权限（用户管理、更新、协议配置等仅 admin 可见/可用）。
  - 在 UI 侧定义能力集（capabilities）并由登录后获取的用户信息驱动，控制路由访问与按钮显隐。

## 页面规划与交互细化
- 设备列表（Devices）
  - 列：名称/描述、类型、标签、状态（由点位推导）、上报时间、所属分组
  - 操作：批量选择、批量更新/移动/删除、快速搜索（名称/描述/类型/标签）
  - 性能：虚拟滚动、分页/无限加载；增量更新合并
- 设备详情（Device Detail）
  - 概览：关键信息卡片（版本、联机状态、最近更新、标签）
  - 点位（Points）：可编辑表格（数值/文本/枚举/单位/偏移/缩放），支持内联编辑与批量提交（POST /points）
  - 命令与参数：区分即时命令与配置参数；支持一次性推送与保存模板
  - 关联：所属分组、适用规则、文件分发、更新任务历史
- 分组（Groups）
  - 扁平/分层切换视图：支持标签/规则驱动的动态集合（弱化固定树）
  - 拖拽移动节点（最终调用 /parents POST move 或 PUT copy）
- 规则（Rules）
  - 以“条件-动作”可视化编辑器表达；版本一先以表单式配置，后续再增强可视化
- 协议与网络（Modbus/CAN/NetworkManager/...）
  - 统一的“连接配置 + 通道/寄存器/点位映射 + 诊断”布局；尽量复用表格与表单组件
- 指标与监控（Metrics）
  - 实时曲线与状态卡片；订阅实时数据，提供采样与抗抖；必要时提供时间范围（若后端提供历史）
- 软件更新（Updates）
  - 任务列表与详情；进度/错误实时反馈（PointTypeSwUpdateRunning/Error/PercComplete）
- 文件（Files）
  - 上传、版本、分发到设备/分组，进度与结果可查
- 消息（Messages/Notifications）
  - 用户维度通知管理、历史列表、筛选与查看
- 用户与角色（Admin）
  - 用户列表、基本信息编辑、角色点位、登录令牌（jwt）管理
- 设置（Settings）
  - 系统参数、NATS/WS 连接测试、调试工具（仅 admin）

## 实时同步与一致性
- 首选：NATS over WebSocket
  - 使用前端 lib（frontend/lib）或基于 nats.ws 的 TS 封装，订阅节点/点位主题；收到消息后增量更新 Pinia store。
  - 断线重连 + 指数退避；重连后触发一次全量校准（GET /v1/nodes）。
- 兜底：轮询（默认 4s）
  - 与现 Elm 一致的 tick，每 4 秒拉取一次并合并变化；仅在 WS 不可用或短暂失连时启用。
- 去抖与批量
  - 表单/表格编辑在前端合并，延迟 300-500ms 提交，失败回滚与错误提示。

## 类型定义与数据访问层（TS）
- 核心 TS 接口（示例）
  - NodeEdge、Point、StandardResponse、NodeMove/Copy/Delete、AuthPayload、Role
- Axios 封装
  - BaseURL、超时、重试策略、响应统一处理、401 自动登出
- NATS 客户端
  - 连接配置（服务器地址、重连）、统一订阅与事件分发、与 store 解耦

## 构建集成与并行迁移
- 开发期
  - Vite 本地开发，设置代理转发 /v1 与 WS 到后端；快速迭代。
- 灰度上线
  - Go 服务器内置静态资源时，将新前端构建产物部署到 /app-v2（或 /v2）路径下；保留旧 Elm 前端为默认。
  - 顶部加“切换到新版 UI”入口；收集反馈与问题。
- 验证与回退
  - 两套前端共享后端数据；问题时可快速回退到 Elm。

## 里程碑与工作量（建议）
- M1（1-2 周）：需求梳理与 IA、技术架构、认证与基础路由、基础组件库选型落地
- M2（2-3 周）：设备列表与详情（点位编辑、移动/复制/删除），NATS 实时 + 轮询兜底
- M3（2 周）：规则、指标/监控、软件更新的最小可用版本
- M4（2 周）：协议与网络模块第一版（选 1-2 个优先模块，如 Modbus、NetworkManager）
- M5（1 周）：文件、消息中心与用户/角色管理
- M6（1 周）：打包集成、灰度、文档与培训、性能与可用性优化

## 风险与应对
- 专用模块复杂度高（Modbus/Rules 等）：分阶段落地，先覆盖高频操作与读-only，再逐步开放写能力。
- 实时一致性：NATS 主题与消息格式需要对齐验证；准备良好的重连与全量校准策略。
- 权限边界：以“后端过滤 + 前端能力开关”双重兜底，避免越权 UI。
- 历史/趋势数据：若后端 API 未完全覆盖，先提供实时 + 近期缓存；历史曲线作为增量能力。

## 架构示意（简图）
我用一张图概览前后端交互与构成。

（图中节点均为概念组件，具体命名以实现为准）

- Vue App
  - Router、Pinia、UI、Charts、API Service（Axios）、Realtime Service（NATS WS）
- Go HTTP API
  - /v1/* 路由、JWT/Token 验证、静态资源
- NATS
  - 与各 Client（Modbus/Rules/...）交互，前端通过 WS 订阅
- Store（SQLite）
  - 数据持久化

我将渲染一个简单的体系结构图：

【如图未显示，请滚动或展开】

