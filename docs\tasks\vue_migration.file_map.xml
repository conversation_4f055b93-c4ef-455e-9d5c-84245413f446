<file-map>
  <frontend-elm>
    <dir path="frontend/src">Elm 源码根目录（基于 elm-spa）。</dir>
    <file path="frontend/elm.json">Elm 项目配置。</file>
    <file path="frontend/elm-watch.json">开发期构建到 public/dist/elm.js。</file>
    <file path="frontend/public/index.html">旧版入口，加载 /dist/elm.js 与 /main.js。</file>
    <file path="frontend/public/main.js">挂载 Elm 应用、localStorage 端口交互。</file>
    <file path="frontend/public/styles.css">全局样式。</file>
    <file path="frontend/embed.go">go:embed frontend/public/*。</file>
    <file path="envsetup.sh">siot_build_frontend 构建并 gzip frontend/public/dist/elm.js。</file>
  </frontend-elm>

  <backend-serving>
    <file path="server/server.go">选择静态资源 FS（dev: ./frontend/public; prod: embed FS）。</file>
    <file path="api/server.go">PublicHandler = http.FileServer(args.Filesystem)；/v1 路由到 V1ApiHandler；根路径支持 WS 升级代理到 NATS。</file>
  </backend-serving>

  <api-elmsrc>
    <file path="frontend/src/Api/Auth.elm">POST /v1/auth</file>
    <file path="frontend/src/Api/Node.elm">GET/POST/DELETE /v1/nodes、/parents、/points、/not</file>
    <file path="frontend/src/Pages/SignIn.elm">登录流程与 GotUser 处理</file>
    <file path="frontend/src/Pages/Home_.elm">业务主页面（包含 Http.Error 401 处理）</file>
  </api-elmsrc>

  <migration-target>
    <dir path="frontend-vue/">建议新建：Vue + TS 源码目录。</dir>
    <out path="frontend/public/app-v2">Vite 输出目录（通过 base 与 build.outDir 配置）。</out>
    <route-prefix>/app-v2</route-prefix>
  </migration-target>
</file-map>

