import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig(({ command }) => ({
  plugins: [vue()],
  // 开发模式使用根路径，生产构建使用 /app-v2/
  base: command === 'serve' ? '/' : '/app-v2/',
  build: {
    outDir: '../frontend/public/app-v2',
    emptyOutDir: true,
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  server: {
    port: 3000,
    proxy: {
      // 代理 API 请求到后端
      '/v1': {
        target: 'http://localhost:8080',
        changeOrigin: true,
      },
      // 代理 WebSocket 到 NATS
      '/ws': {
        target: 'ws://localhost:8080',
        ws: true,
        changeOrigin: true,
      },
    },
  },
}))
