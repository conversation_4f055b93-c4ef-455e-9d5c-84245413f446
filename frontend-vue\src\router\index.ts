import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/devices'
  },
  {
    path: '/sign-in',
    name: 'SignIn',
    component: () => import('@/views/SignIn.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/devices',
    name: 'Devices',
    component: () => import('@/views/Devices.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/devices/:id',
    name: 'DeviceDetail',
    component: () => import('@/views/DeviceDetail.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/updates',
    name: 'Updates',
    component: () => import('@/views/Updates.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/files',
    name: 'Files',
    component: () => import('@/views/Files.vue'),
    meta: { requiresAuth: true }
  }
]

const router = createRouter({
  history: createWebHistory('/app-v2/'),
  routes
})

// 路由守卫（暂时简单实现，后续完善）
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('siot-token')
  
  if (to.meta.requiresAuth && !token) {
    next('/sign-in')
  } else if (to.path === '/sign-in' && token) {
    next('/devices')
  } else {
    next()
  }
})

export default router
