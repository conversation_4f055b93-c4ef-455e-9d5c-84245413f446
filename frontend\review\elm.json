{"type": "application", "source-directories": ["src"], "elm-version": "0.19.1", "dependencies": {"direct": {"elm/core": "1.0.5", "elm/json": "1.1.3", "elm/project-metadata-utils": "1.0.2", "jfmengels/elm-review": "2.13.1", "jfmengels/elm-review-code-style": "1.1.4", "jfmengels/elm-review-common": "1.3.3", "jfmengels/elm-review-debug": "1.0.8", "jfmengels/elm-review-documentation": "2.0.4", "jfmengels/elm-review-simplify": "2.1.3", "jfmengels/elm-review-unused": "1.2.0", "stil4m/elm-syntax": "7.3.2"}, "indirect": {"elm/bytes": "1.0.8", "elm/html": "1.0.0", "elm/parser": "1.1.0", "elm/random": "1.0.0", "elm/regex": "1.0.0", "elm/time": "1.0.0", "elm/virtual-dom": "1.0.3", "elm-explorations/test": "2.2.0", "miniBill/elm-unicode": "1.1.0", "pzp1997/assoc-list": "1.0.0", "rtfeldman/elm-hex": "1.0.0", "stil4m/structured-writer": "1.0.3"}}, "test-dependencies": {"direct": {}, "indirect": {}}}