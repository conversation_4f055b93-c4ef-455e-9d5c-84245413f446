<frontend-migration>
  <summary>
    <reason>
      现有 Elm 前端学习成本高、以树形结构为主不利于最终用户。目标以 Vue 3 + TypeScript 重构，转为任务导向与扁平化导航，保留旧版作为过渡。
    </reason>
    <scope>
      保持后端接口与嵌入式静态资源结构不变（Go 服务器 /v1 API 与 NATS WS 代理），新增基于 Vite 的前端并集成到现有 frontend/public 下的 /app-v2 路径，灰度上线。
    </scope>
    <restore>
      如需“恢复记忆”，先阅读 file-map 与 api-map，然后在仓库中打开对应文件路径；所有规划与任务在本目录 XML 文件中可复现。
    </restore>
  </summary>

  <principles>
    <item>用户导向：移除树形主导航，采用列表/搜索/筛选与标签。</item>
    <item>渐进式迁移：/app-v2 为新 UI 路由前缀，旧 Elm 暂时保留，直至新 UI 稳定后移除。</item>
    <item>最小改动后端：沿用 /v1/* REST 与 NATS over WebSocket。</item>
    <item>工程化：Vite + Vue 3 + TS + Pinia + Vue Router；统一 API 封装与错误处理。</item>
    <item>可用性与性能：虚拟滚动、批量更新、断线重连、清晰的错误与权限提示。</item>
  </principles>

  <tech-stack>
    <build>Vite</build>
    <framework>Vue3-CompositionAPI</framework>
    <language>TypeScript</language>
    <router>VueRouter</router>
    <state>Pinia</state>
    <http>Axios</http>
    <realtime>nats.ws (优先) + 4s 轮询兜底</realtime>
    <ui-library>Element Plus (工业风格，文档完善)</ui-library>
    <charts>ECharts</charts>
    <utils>VueUse (组合式函数)</utils>
    <testing>Vitest (后续)</testing>
  </tech-stack>

  <architecture>
    <folders>
      <folder name="frontend/public/app-v2">新前端构建产物根目录（Go 内置静态资源可直接服务）。</folder>
      <folder name="frontend-vue/">（建议新增）源码根目录，vite.config.ts 输出至 ../frontend/public/app-v2。</folder>
      <folder name="frontend/lib">可复用的 NATS JS 封装（如需）。</folder>
    </folders>
    <modules>
      <module name="api">封装 /v1/*（auth/nodes/points/parents/not）。统一拦截器注入 Authorization。</module>
      <module name="domain">TS 类型：Node、Point、Response、NodeMove/Copy/Delete、Auth。</module>
      <module name="stores">Pinia 实体仓库与选择器（按类型/标签/搜索）。</module>
      <module name="realtime">NATS 连接管理、订阅与事件分发；重连后触发全量校准。</module>
      <module name="views">任务导向页面与节点类型页面（不提供树形主导航）。</module>
    </modules>
  </architecture>

  <navigation>
    <topbar>全局搜索、账号菜单、系统状态（可选）。</topbar>
    <sidebar>Dashboard、Devices、Groups、Protocols、Rules、Metrics、Updates、Files、Messages、Users、Settings。</sidebar>
    <detail>设备详情页：概览｜点位｜命令/参数｜关联｜日志（可选）。</detail>
  </navigation>

  <realtime>
    <primary>NATS over WebSocket（api/server.go 对 / 的 WS Upgrade 代理取决于 NatsWSPort）。</primary>
    <fallback>4s 轮询 GET /v1/nodes 并合并变化。</fallback>
    <consistency>前端批量/去抖更新；失败回滚并提示。</consistency>
  </realtime>

  <security>
    <auth>POST /v1/auth 获取 token；localStorage 持久化；Axios 拦截器注入 Bearer。</auth>
    <on401>统一处理：清除本地状态并跳转登录页。</on401>
    <roles>基于用户节点/点位决定可见菜单与操作（前端能力开关 + 后端过滤双重保障）。</roles>
  </security>

  <milestones>
    <m id="M1">
      <goal>基础骨架：Vite/Vue/TS、Router/Pinia、Axios 封装、登录页与守卫、开发代理。</goal>
      <accept>能成功登录，令牌持久化与路由守卫生效；/app-v2 可访问。</accept>
    </m>
    <m id="M2">
      <goal>Devices 列表与详情（点位读/写、移动/删除），轮询兜底。</goal>
      <accept>列表可搜索/筛选/分页；详情可编辑点位并提交。</accept>
    </m>
    <m id="M3">
      <goal>集成 NATS 实时；断线重连与全量校准。</goal>
      <accept>实时点位刷新，断线后自动恢复。</accept>
    </m>
    <m id="M4">
      <goal>Groups/Rules/Metrics/Updates/Files/Users/<USER>/goal>
      <accept>核心操作闭环跑通，权限与错误提示清晰。</accept>
    </m>
    <m id="M5">
      <goal>打包集成与灰度：构建产物入 frontend/public/app-v2；切换入口。</goal>
      <accept>Go 服务器同时可服务旧 Elm 与新 Vue；出现问题可回退。</accept>
    </m>
  </milestones>

  <acceptance>
    <item>前端构建产物不覆盖现有 /dist/elm.js；新产物位于 /app-v2。</item>
    <item>登录/权限、设备列表/详情、点位编辑、节点移动/复制/删除全通过手测与基本自动化测试。</item>
    <item>NATS 实时在开启 WS 时可用；关闭 WS 时轮询兜底。</item>
  </acceptance>

  <integration>
    <dev>Vite 代理 /v1 与 WS 到后端 HTTP 与 NATS WS 端口。</dev>
    <build>Vite 输出目录配置到 frontend/public/app-v2；无需改动 Go 静态资源嵌入逻辑。</build>
    <serve>api/server.go 使用 http.FileServer(args.Filesystem) 直接服务 public；/app-v2 路由下页面由 Vue 接管。</serve>
  </integration>

  <restore-guide>
    <step>阅读 file-map 了解嵌入链路与关键文件。</step>
    <step>阅读 api-map 获取全部 /v1 接口与认证要求。</step>
    <step>阅读 tasks 获取当前进度、下一步与验收标准。</step>
    <step>阅读 decisions 理解架构选择与风险缓解。</step>
  </restore-guide>
</frontend-migration>

