<template>
  <div class="files-container">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside width="200px">
        <el-menu
          default-active="/files"
          router
          class="sidebar-menu"
        >
          <el-menu-item index="/devices">
            <el-icon><Monitor /></el-icon>
            <span>设备管理</span>
          </el-menu-item>
          <el-menu-item index="/updates">
            <el-icon><Download /></el-icon>
            <span>软件更新</span>
          </el-menu-item>
          <el-menu-item index="/files">
            <el-icon><Folder /></el-icon>
            <span>文件管理</span>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 主内容区 -->
      <el-container>
        <!-- 顶部栏 -->
        <el-header>
          <div class="header-content">
            <div class="header-left">
              <h2>文件管理</h2>
            </div>
          </div>
        </el-header>

        <!-- 主内容 -->
        <el-main>
          <el-card>
            <template #header>
              <div class="card-header">
                <span>文件列表</span>
                <el-button type="primary" :icon="Upload">上传文件</el-button>
              </div>
            </template>

            <el-empty description="暂无文件" />
          </el-card>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { Monitor, Download, Folder, Upload } from '@element-plus/icons-vue'
</script>

<style scoped>
.files-container {
  height: 100vh;
}

.sidebar-menu {
  height: 100vh;
  border-right: 1px solid #e6e6e6;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 16px;
  border-bottom: 1px solid #e6e6e6;
}

.header-left h2 {
  margin: 0;
  color: #303133;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
