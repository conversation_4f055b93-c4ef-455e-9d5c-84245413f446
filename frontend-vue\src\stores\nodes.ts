import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { NodeEdge } from '@/types'

export const useNodesStore = defineStore('nodes', () => {
  // 状态
  const nodes = ref<Map<string, NodeEdge>>(new Map())
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const nodesList = computed(() => Array.from(nodes.value.values()))
  
  const deviceNodes = computed(() => 
    nodesList.value.filter(node => node.type === 'device')
  )

  // 搜索和筛选
  function searchNodes(query: string) {
    if (!query.trim()) return nodesList.value
    
    const lowerQuery = query.toLowerCase()
    return nodesList.value.filter(node => 
      node.id.toLowerCase().includes(lowerQuery) ||
      node.points.some(point => 
        point.text?.toLowerCase().includes(lowerQuery) ||
        point.key.toLowerCase().includes(lowerQuery)
      )
    )
  }

  function getNodesByType(type: string) {
    return nodesList.value.filter(node => node.type === type)
  }

  // 动作
  function setNodes(nodesList: NodeEdge[]) {
    nodes.value.clear()
    nodesList.forEach(node => {
      nodes.value.set(node.id, node)
    })
  }

  function addNode(node: NodeEdge) {
    nodes.value.set(node.id, node)
  }

  function updateNode(node: NodeEdge) {
    nodes.value.set(node.id, node)
  }

  function removeNode(nodeId: string) {
    nodes.value.delete(nodeId)
  }

  function getNode(nodeId: string) {
    return nodes.value.get(nodeId)
  }

  function setLoading(isLoading: boolean) {
    loading.value = isLoading
  }

  function setError(errorMessage: string | null) {
    error.value = errorMessage
  }

  return {
    nodes,
    loading,
    error,
    nodesList,
    deviceNodes,
    searchNodes,
    getNodesByType,
    setNodes,
    addNode,
    updateNode,
    removeNode,
    getNode,
    setLoading,
    setError
  }
})
