{"name": "simpleiot-vue", "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.0", "pinia": "^2.1.0", "axios": "^1.6.0", "element-plus": "^2.4.0", "@element-plus/icons-vue": "^2.3.0", "@vueuse/core": "^10.7.0", "echarts": "^5.4.0", "vue-echarts": "^6.6.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "@vue/tsconfig": "^0.5.0", "typescript": "^5.3.0", "vite": "^5.0.0", "vue-tsc": "^1.8.0"}, "engines": {"node": ">=18.0.0"}}