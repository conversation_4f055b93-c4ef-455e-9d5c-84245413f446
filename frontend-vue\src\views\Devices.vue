<template>
  <div class="devices-container">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside width="200px">
        <el-menu
          default-active="/devices"
          router
          class="sidebar-menu"
        >
          <el-menu-item index="/devices">
            <el-icon><Monitor /></el-icon>
            <span>设备管理</span>
          </el-menu-item>
          <el-menu-item index="/updates">
            <el-icon><Download /></el-icon>
            <span>软件更新</span>
          </el-menu-item>
          <el-menu-item index="/files">
            <el-icon><Folder /></el-icon>
            <span>文件管理</span>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 主内容区 -->
      <el-container>
        <!-- 顶部栏 -->
        <el-header>
          <div class="header-content">
            <div class="header-left">
              <h2>设备列表</h2>
            </div>
            <div class="header-right">
              <el-input
                v-model="searchQuery"
                placeholder="搜索设备..."
                :prefix-icon="Search"
                style="width: 300px; margin-right: 16px;"
              />
              <el-dropdown @command="handleUserAction">
                <el-button type="primary">
                  <el-icon><User /></el-icon>
                  {{ authStore.user?.email || '用户' }}
                  <el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="logout">退出登录</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </el-header>

        <!-- 主内容 -->
        <el-main>
          <el-card>
            <template #header>
              <div class="card-header">
                <span>设备列表</span>
                <el-button type="primary" :icon="Plus">添加设备</el-button>
              </div>
            </template>

            <!-- 设备表格 -->
            <el-table 
              :data="filteredDevices" 
              v-loading="nodesStore.loading"
              style="width: 100%"
            >
              <el-table-column prop="id" label="设备ID" width="200" />
              <el-table-column prop="type" label="类型" width="120" />
              <el-table-column label="描述" width="200">
                <template #default="scope">
                  {{ getNodeDescription(scope.row) }}
                </template>
              </el-table-column>
              <el-table-column label="状态" width="100">
                <template #default="scope">
                  <el-tag type="success">在线</el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作">
                <template #default="scope">
                  <el-button 
                    type="primary" 
                    size="small"
                    @click="viewDevice(scope.row)"
                  >
                    查看详情
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Monitor, Download, Folder, Search, User, ArrowDown, Plus 
} from '@element-plus/icons-vue'
import { useAuthStore, useNodesStore } from '@/stores'
import type { NodeEdge } from '@/types'

const router = useRouter()
const authStore = useAuthStore()
const nodesStore = useNodesStore()

const searchQuery = ref('')

// 计算属性
const filteredDevices = computed(() => {
  if (!searchQuery.value) {
    return nodesStore.deviceNodes
  }
  return nodesStore.searchNodes(searchQuery.value).filter(node => node.type === 'device')
})

// 方法
function getNodeDescription(node: NodeEdge) {
  const descPoint = node.points.find(p => p.key === 'description')
  return descPoint?.text || '无描述'
}

function viewDevice(device: NodeEdge) {
  router.push(`/devices/${device.id}`)
}

async function handleUserAction(command: string) {
  if (command === 'logout') {
    try {
      await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      
      authStore.clearAuth()
      ElMessage.success('已退出登录')
      router.push('/sign-in')
    } catch {
      // 用户取消
    }
  }
}

// 生命周期
onMounted(() => {
  // TODO: 加载设备列表
  // 暂时添加一些模拟数据
  nodesStore.setNodes([
    {
      id: 'device-001',
      type: 'device',
      points: [
        { typ: 'description', key: 'description', time: Date.now(), value: 0, text: '温度传感器', tombstone: 0 },
        { typ: 'value', key: 'temperature', time: Date.now(), value: 25.5, text: '25.5°C', tombstone: 0 }
      ]
    },
    {
      id: 'device-002', 
      type: 'device',
      points: [
        { typ: 'description', key: 'description', time: Date.now(), value: 0, text: '湿度传感器', tombstone: 0 },
        { typ: 'value', key: 'humidity', time: Date.now(), value: 60, text: '60%', tombstone: 0 }
      ]
    }
  ])
})
</script>

<style scoped>
.devices-container {
  height: 100vh;
}

.sidebar-menu {
  height: 100vh;
  border-right: 1px solid #e6e6e6;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 16px;
  border-bottom: 1px solid #e6e6e6;
}

.header-left h2 {
  margin: 0;
  color: #303133;
}

.header-right {
  display: flex;
  align-items: center;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
