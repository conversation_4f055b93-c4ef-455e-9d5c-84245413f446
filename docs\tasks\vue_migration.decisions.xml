<decisions>
  <decision id="ui-architecture">
    <choice>Vue 3 + TS + Pinia + Vue Router + Vite</choice>
    <alternatives>React + Redux Toolkit; SvelteKit</alternatives>
    <rationale>Vue 生态成熟、表格/表单生态好、学习曲线低于 Elm。</rationale>
    <impacts>需要新增构建与目录；不改动后端。</impacts>
  </decision>

  <decision id="navigation">
    <choice>任务导向侧边栏 + 顶部搜索；完全移除树形主导航。</choice>
    <alternatives>继续以树为主导航；保留为二级视图。</alternatives>
    <rationale>最终用户易用性更好，聚焦常见任务（设备、更新、规则等）。</rationale>
    <impacts>需要新增扁平列表与搜索索引。</impacts>
  </decision>

  <decision id="realtime">
    <choice>优先 NATS WS；fallback 轮询。</choice>
    <alternatives>仅轮询。</alternatives>
    <rationale>具备实时能力时体验更佳，同时保证弱网可用性。</rationale>
    <impacts>需要实现订阅与增量合并策略。</impacts>
  </decision>

  <decision id="deployment">
    <choice>新前端构建到 frontend/public/app-v2；旧前端保留。</choice>
    <alternatives>替换现有 /dist/elm.js。</alternatives>
    <rationale>灰度、可回退。</rationale>
    <impacts>Go 静态资源无需调整；仅新增目录。</impacts>
  </decision>

  <risks>
    <risk>
      <desc>专用模块复杂（Modbus/NetworkManager/Rules）。</desc>
      <mitigation>分阶段实现，先读-only 与高频写，再扩展。</mitigation>
    </risk>
    <risk>
      <desc>实时一致性与重连。</desc>
      <mitigation>统一重连策略与全量校准；提供手动刷新。</mitigation>
    </risk>
    <risk>
      <desc>权限边界不清导致越权 UI。</desc>
      <mitigation>后端过滤 + 前端能力开关；401 自动登出。</mitigation>
    </risk>
  </risks>
</decisions>

