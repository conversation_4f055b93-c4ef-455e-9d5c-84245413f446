# Metrics

An important part of maintaining healthy systems is to monitor metrics for the
application and system. SIOT can collect metrics for:

- the system
- the SIOT application
- any named processes

For the named process, if there are multiple processes of the same name, then we
add values for all processes found.

## System Metrics

![system-metrics](images/metrics-system.png)

## SIOT Application Metrics

![app-metrics](images/metrics-app.png)

## Named Process Metrics

![proc-metrics](images/metrics-proc.png)
