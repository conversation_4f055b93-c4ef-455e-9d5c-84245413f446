import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User } from '@/types'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('siot-token'))

  // 计算属性
  const isAuthenticated = computed(() => !!token.value)

  // 动作
  function setAuth(userData: User, authToken: string) {
    user.value = userData
    token.value = authToken
    localStorage.setItem('siot-token', authToken)
  }

  function clearAuth() {
    user.value = null
    token.value = null
    localStorage.removeItem('siot-token')
  }

  // 初始化时检查本地存储
  function initAuth() {
    const storedToken = localStorage.getItem('siot-token')
    if (storedToken) {
      token.value = storedToken
      // TODO: 验证 token 有效性，获取用户信息
    }
  }

  return {
    user,
    token,
    isAuthenticated,
    setAuth,
    clearAuth,
    initAuth
  }
})
