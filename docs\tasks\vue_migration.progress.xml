<progress>
  <current phase="setup" task="setup-routing" />

  <next>
    <t phase="setup" id="setup-routing" />
    <t phase="setup" id="setup-state" />
    <t phase="setup" id="setup-axios" />
    <t phase="auth" id="auth-page" />
    <t phase="auth" id="auth-guard" />
    <t phase="core" id="devices-list" />
    <t phase="core" id="devices-detail" />
    <t phase="core" id="updates" />
    <t phase="core" id="files" />
    <t phase="core" id="devices-mutate" />
  </next>

  <completed>
    <t phase="setup" id="setup-structure" />
  </completed>

  <log>
    <entry>初始化进度文件：将当前任务设为 setup-structure；UI 库为 Element Plus；新 UI 路由前缀 /app-v2；树形主导航移除。</entry>
    <entry>规划类文档已完成：plan.xml、api_map.xml、file_map.xml、decisions.xml、types.xml、clients.xml、wakeup.xml。</entry>
    <entry>setup-structure 任务完成：创建了完整的 Vue 3 + TS + Vite 基础框架，包括 vite.config.ts（输出到 /app-v2）、package.json（Element Plus + VueUse）、路由配置、Pinia 状态管理、TypeScript 类型定义、登录页面和设备管理页面。所有文件已创建，下一步是 setup-routing。</entry>
  </log>
</progress>

