<progress>
  <current phase="setup" task="setup-structure" />

  <next>
    <t phase="setup" id="setup-routing" />
    <t phase="setup" id="setup-state" />
    <t phase="setup" id="setup-axios" />
    <t phase="auth" id="auth-page" />
    <t phase="auth" id="auth-guard" />
    <t phase="core" id="devices-list" />
    <t phase="core" id="devices-detail" />
    <t phase="core" id="updates" />
    <t phase="core" id="files" />
    <t phase="core" id="devices-mutate" />
  </next>

  <completed>
    <!-- 任务完成后，把对应 <t id="..."/> 移到这里，保留 phase 与 id 对应 tasks.xml -->
  </completed>

  <log>
    <entry>初始化进度文件：将当前任务设为 setup-structure；UI 库为 Ant Design Vue；新 UI 路由前缀 /app-v2；树形主导航移除。</entry>
    <entry>规划类文档已完成：plan.xml、api_map.xml、file_map.xml、decisions.xml、types.xml、clients.xml、wakeup.xml。</entry>
  </log>
</progress>

