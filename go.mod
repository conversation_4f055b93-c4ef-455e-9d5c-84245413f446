module github.com/simpleiot/simpleiot

require (
	github.com/Wifx/gonetworkmanager/v2 v2.2.0
	github.com/adrianmo/go-nmea v1.10.0
	github.com/beevik/ntp v1.4.3
	github.com/blang/semver/v4 v4.0.0
	github.com/cavaliercoder/grab v2.0.0+incompatible
	github.com/cosmtrek/air v1.61.2
	github.com/dim13/cobs v0.1.0
	github.com/donovanhide/eventsource v0.0.0-20210830082556-c59027999da0
	github.com/fsnotify/fsnotify v1.9.0
	github.com/go-audio/wav v1.1.0
	github.com/goccy/go-yaml v1.17.1
	github.com/godbus/dbus/v5 v5.1.0
	github.com/golang-jwt/jwt/v4 v4.5.2
	github.com/golang/protobuf v1.5.4
	github.com/google/go-cmp v0.7.0
	github.com/google/uuid v1.6.0
	github.com/influxdata/influxdb-client-go/v2 v2.14.0
	github.com/jacobsa/go-serial v0.0.0-20180131005756-15cf729a72d4
	github.com/kevinburke/twilio-go v0.0.0-20240716172313-813590983ccc
	github.com/kjx98/crc16 v0.0.0-20190915014410-d407ba22e1b5
	github.com/koding/websocketproxy v0.0.0-20181220232114-7ed82d81a28c
	github.com/nats-io/nats-server/v2 v2.11.1
	github.com/nats-io/nats.go v1.41.2
	github.com/oklog/run v1.1.0
	github.com/pkg/errors v0.9.1
	github.com/shirou/gopsutil/v3 v3.24.5
	github.com/simpleiot/canparse v0.0.0-20221213134514-84672be992d4
	github.com/simpleiot/mdns v0.0.1
	go.bug.st/serial v1.6.4
	go.einride.tech/can v0.12.2
	golang.org/x/exp v0.0.0-20250408133849-7e4ce0ab07d0
	golang.org/x/lint v0.0.0-20241112194109-818c5a804067
	google.golang.org/protobuf v1.36.6
	modernc.org/sqlite v1.37.0
)

require (
	dario.cat/mergo v1.0.1 // indirect
	github.com/apapsch/go-jsonmerge/v2 v2.0.0 // indirect
	github.com/bep/godartsass/v2 v2.5.0 // indirect
	github.com/bep/golibsass v1.2.0 // indirect
	github.com/creack/goselect v0.1.3 // indirect
	github.com/creack/pty v1.1.24 // indirect
	github.com/dustin/go-humanize v1.0.1 // indirect
	github.com/fatih/color v1.18.0 // indirect
	github.com/go-audio/audio v1.0.0 // indirect
	github.com/go-audio/riff v1.0.0 // indirect
	github.com/go-ole/go-ole v1.3.0 // indirect
	github.com/gobwas/glob v0.2.3 // indirect
	github.com/gofrs/uuid v4.4.0+incompatible // indirect
	github.com/gohugoio/hugo v0.146.5 // indirect
	github.com/golang/mock v1.6.0 // indirect
	github.com/google/go-tpm v0.9.3 // indirect
	github.com/gorilla/websocket v1.5.3 // indirect
	github.com/influxdata/line-protocol v0.0.0-20210922203350-b1ad95c89adf // indirect
	github.com/kevinburke/go-types v0.0.0-20240719050749-165e75e768f7 // indirect
	github.com/kevinburke/rest v0.0.0-20240617045629-3ed0ad3487f0 // indirect
	github.com/klauspost/compress v1.18.0 // indirect
	github.com/lufia/plan9stats v0.0.0-20250317134145-8bc96cf8fc35 // indirect
	github.com/mattn/go-colorable v0.1.14 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/mattn/go-runewidth v0.0.15 // indirect
	github.com/miekg/dns v1.1.65 // indirect
	github.com/minio/highwayhash v1.0.3 // indirect
	github.com/nats-io/jwt/v2 v2.7.4 // indirect
	github.com/nats-io/nkeys v0.4.11 // indirect
	github.com/nats-io/nuid v1.0.1 // indirect
	github.com/ncruces/go-strftime v0.1.9 // indirect
	github.com/oapi-codegen/runtime v1.1.1 // indirect
	github.com/pelletier/go-toml v1.9.5 // indirect
	github.com/pelletier/go-toml/v2 v2.2.4 // indirect
	github.com/power-devops/perfstat v0.0.0-20240221224432-82ca36839d55 // indirect
	github.com/remyoudompheng/bigfft v0.0.0-20230129092748-24d4a6f8daec // indirect
	github.com/shoenig/go-m1cpu v0.1.6 // indirect
	github.com/spf13/afero v1.14.0 // indirect
	github.com/spf13/cast v1.7.1 // indirect
	github.com/tdewolff/parse/v2 v2.7.23 // indirect
	github.com/tklauser/go-sysconf v0.3.15 // indirect
	github.com/tklauser/numcpus v0.10.0 // indirect
	github.com/ttacon/builder v0.0.0-20170518171403-c099f663e1c2 // indirect
	github.com/ttacon/libphonenumber v1.2.1 // indirect
	github.com/yusufpapurcu/wmi v1.2.4 // indirect
	golang.org/x/crypto v0.37.0 // indirect
	golang.org/x/mod v0.24.0 // indirect
	golang.org/x/net v0.39.0 // indirect
	golang.org/x/sync v0.13.0 // indirect
	golang.org/x/sys v0.32.0 // indirect
	golang.org/x/text v0.24.0 // indirect
	golang.org/x/time v0.11.0 // indirect
	golang.org/x/tools v0.32.0 // indirect
	modernc.org/libc v1.63.0 // indirect
	modernc.org/mathutil v1.7.1 // indirect
	modernc.org/memory v1.10.0 // indirect
)

go 1.23.0

toolchain go1.24.1
