# Simple IoT Clients

**Contents**

<!-- toc -->

Most functionality in Simple IoT is implemented in Clients.

![app-arch](images/arch-app.png)

Each client can be configured by one or more nodes in the SIOT store graph.
These nodes may be created by a user, a process that detects new plug and play
hardware, or other clients.

A client interacts with the system by listening for new points it is interested
in and sending out points as it acquires new data.

## Creating new clients

See [Development](development.md) for information on how to set up a development
system.

Simple IoT provides utilities that assist in creating new clients. See the
[Go package documentation](https://pkg.go.dev/github.com/simpleiot/simpleiot/client)
for more information. A client manager is created for each client type. This
manager instantiates new client instances when new nodes are detected and then
sends point updates to the client. Two levels of nodes are currently supported
for client configuration. An example of this would be a Rule node that has
Condition and Action child nodes.

A "disabled" option is useful and should be considered for every new client.

Creating a new client typically requires the following steps:

1. add any new node and points types to
   [`schema.go`](https://github.com/simpleiot/simpleiot/blob/master/data/schema.go),
   [`Node.elm`](https://github.com/simpleiot/simpleiot/blob/master/frontend/src/Api/Node.elm),
   and
   [`Point.elm`](https://github.com/simpleiot/simpleiot/blob/master/frontend/src/Api/Point.elm).
   Please try to reuse existing point types when possible.
1. create a new client in
   [`client/`](https://github.com/simpleiot/simpleiot/tree/master/client)
   directory. A client is defined by a type that satisfies the
   [`Client interface`](https://pkg.go.dev/github.com/simpleiot/simpleiot/client#Client).
   A constructor must also be defined that is passed to
   [`NewManager`](https://pkg.go.dev/github.com/simpleiot/simpleiot/client#NewManager)
   and a struct that represents the client data. The name of the struct must
   match the node type -- for instance a node of type `canBus` needs to be
   defined by a struct named `CanBus`. Additionally, each field of the client
   struct must have point tags. This allows us to automatically create and
   modify client structs from arrays of node points.
1. create a new manager for the client in
   [`client/client.go`](https://github.com/simpleiot/simpleiot/blob/master/client/client.go)
1. Create an Elm UI for the clent in
   [`frontend/src/Components/`](https://github.com/simpleiot/simpleiot/tree/master/frontend/src/Components)
1. Create plumbing for new NodeXYZ in
   [`frontend/src/Pages/Home_.elm`](https://github.com/simpleiot/simpleiot/blob/master/frontend/src/Pages/Home_.elm).
   Note, this can likely be improved a lot.

It is easiest to copy one of the existing clients to start. The NTP client is
relatively simple and may be a good example.

## Client lifecycle

It is important the clients cleanly implement the
[Run()/Stop() pattern](architecture-app.md#application-lifecycle) and shut down
cleanly when Stop() is called releasing all resources. If nodes are added or
removed, clients are started/stopped. Additionally if a child node of a client
config is added or removed, the entire client is stopped and then restarted.
This relieves the burden on the client from managing the addition/removal of
client functionality. Thus it is very important that clients stop cleanly and
release resources in case they are restarted.

## Message echo

Clients need to be aware of the "echo" problem as they typically subscribe as
well as publish to the `points` subject for the nodes they manage. When they
publish to these subjects, these messages will be echoed back to them. There are
several solutions:

1. create a new NATS connection for the client with the
   [NoEcho](https://docs.nats.io/using-nats/developer/connecting/noecho) option
   set. For this to work, each client will need to establish its own connection
   to the server. This may not work in cases where subjects are aliased into
   authenticated subject namespaces.
2. inspect the `Point` `Origin` field -- if is blank, then it was generated by
   the node that owns the point and does not need to be processed by the client
   generating the data for that node. If is not blank, then the Point was
   generated by a user, rule, or something other than the client owning the node
   and must be processed. This may not always work -- example: user is connected
   to a downstream instance and modifies a point that then propagates upstream
   -- it may get echo'd back to an authenticated client.
3. (investigation stage) A NATS messages header can be populated with the ID of
   the client that sent the message. If it is an authenticated client, then the
   message will not be echo'd on the authenticated client subject namespace of
   the same ID. This information is not stored, so cannot be used for auditing
   purposes.

The SIOT client manager filters out points for the following two scenarios:

1. a point with the same ID as the client and Origin set to a blank string.
1. a point received for a client where Origin matches the client root node ID.

Thus, if you want to set a point in one client and get that point to another
node client, you must set the Origin field. This helps ensure that the Origin
field is used consistently as otherwise stuff won't work.

This gets a little tricky for clients that manage a node and its children (for
instance the rule client -- it has condition and action child nodes). If we
follow the the following rule:

**Clients must set the point Origin field for any point sent to anything other
than than its root node.**

If we following the above rule, then things should work. We may eventally
provide clients with a function to send points that handles this automatically,
but for now it is manual.

See also [tracking who made changes](data.md#tracking-who-made-changes).
