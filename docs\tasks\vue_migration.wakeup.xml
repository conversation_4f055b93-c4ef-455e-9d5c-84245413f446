<wakeup-guide>
  <purpose>
    本指令用于“唤醒”未来无记忆的助理，使其按固定步骤恢复上下文、定位当前任务、继续推进并在完成后正确记录进度与产出。
  </purpose>

  <minimal-prompt>
    粘贴到新会话的第一句话（原样）：
    
    【WAKEUP】Open the SimpleIoT repo and immediately read these files in order:
    1) docs/tasks/vue_migration.wakeup.xml (this guide)
    2) docs/tasks/vue_migration.plan.xml
    3) docs/tasks/vue_migration.file_map.xml
    4) docs/tasks/vue_migration.api_map.xml
    5) docs/tasks/vue_migration.clients.xml
    6) docs/tasks/vue_migration.types.xml
    7) docs/tasks/vue_migration.tasks.xml
    8) docs/tasks/vue_migration.progress.xml (if exists)
    Then: determine the current task from progress.xml (or initialize it), follow the execution policy in wakeup.xml, and proceed. Always update progress.xml after each meaningful step. Never install deps or deploy without explicit permission. The UI library is Ant Design Vue; new UI lives under /app-v2.
  </minimal-prompt>

  <recovery-steps>
    <step>读取 plan.xml 了解目标、原则、技术栈（Ant Design Vue）、里程碑与验收标准。</step>
    <step>读取 file_map.xml 熟悉前端构建产物位置（frontend/public/app-v2）、后端静态资源嵌入链路与关键源码文件。</step>
    <step>读取 api_map.xml 明确 /v1/* 接口、认证方式与静态资源/WS 代理。</step>
    <step>读取 clients.xml 理解 Clients 在系统中的角色与对应的前端模块归属。</step>
    <step>读取 types.xml 明确 TS 类型基线，避免凭空臆测。</step>
    <step>读取 tasks.xml 获取任务分解；如存在 progress.xml 则以其中的 current/ completed 作为权威。</step>
    <step>若 progress.xml 不存在，则新建并初始化当前任务为 tasks.xml 中第一个未完成的任务。</step>
  </recovery-steps>

  <execution-policy>
    <rules>
      <rule>小步快跑：一次只推进一个任务，完成后更新进度并给出下一步建议。</rule>
      <rule>安全验证优先：可运行单元测试、构建、lint 等低成本校验；禁止擅自安装依赖、修改数据库、部署或运行长作业。</rule>
      <rule>最小变更：编辑前先阅读文件；仅修改必要片段；提交前总结改动与影响。</rule>
      <rule>接口与嵌入约束：保持 /v1 接口与静态资源嵌入逻辑不变；新前端输出到 /app-v2。</rule>
      <rule>实时优先 WS：可用则用 NATS WS；不可用时启用 4s 轮询兜底。</rule>
    </rules>
  </execution-policy>

  <task-tracking>
    <source>tasks.xml 为任务清单的“蓝本”。</source>
    <progress-file>进度记录文件：docs/tasks/vue_migration.progress.xml。</progress-file>
    <schema>
      <![CDATA[
      <progress>
        <current phase="setup" task="setup-structure" />
        <completed>
          <t id="..." />
        </completed>
        <log>
          <entry ts="...">简要描述本次会话完成了什么、修改了哪些文件、验证结果如何</entry>
        </log>
      </progress>
      ]]>
    </schema>
    <how-to-use>
      <item>开始工作：若 progress.xml 存在，则读取 <current>；否则初始化为 tasks.xml 中第一条任务。</item>
      <item>推进节奏：完成当前任务后，追加 <t id="..."> 至 <completed>，并将 <current> 指向下一个任务。</item>
      <item>记录日志：每次完成有意义的步骤后，在 <log> 中追加一条 entry，包含“动作要点/文件/验证”。</item>
      <item>不要改写 tasks.xml 的结构；如需增删任务，先在 tasks.xml 追加，再在 progress.xml 同步。</item>
    </how-to-use>
  </task-tracking>

  <when-memory-low>
    <guidance>
      <item>在输出较长时，优先把关键信息整理为文件（docs/tasks 下），并更新 progress.xml 的 <log> 与 <current>。</item>
      <item>如果会话即将结束或上下文将满，先写入“当前状态快照”到 progress.xml，保证下次能无缝恢复。</item>
    </guidance>
  </when-memory-low>

  <failure-handling>
    <case>If 关键 XML 文件缺失</case>
    <action>先在仓库中搜索是否被移动；若确实缺失，向用户说明并请求恢复或允许重建；重建时以 plan.xml 和 tasks.xml 为优先。</action>
    <case>If 接口或文件结构与文档不一致</case>
    <action>以代码真实实现为准，更新对应 xml（api_map、file_map、types），并在 progress.xml 的 <log> 记录差异。</action>
  </failure-handling>

  <ready-checklist>
    <item>UI 组件库：Element Plus（工业风格，文档完善）。</item>
    <item>新 UI 路由前缀：/app-v2；Vite build.outDir 指向 frontend/public/app-v2。</item>
    <item>树形主导航：完全移除；采用任务导向与列表/搜索/筛选/标签。</item>
    <item>默认后端接口：/v1；认证 Bearer token；WS 为 NATS（可选）。</item>
    <item>工具库：VueUse 提供组合式函数；Vitest 用于测试（后续）。</item>
  </ready-checklist>

  <task-templates>
    <task id="setup-structure">
      <goal>创建 Vue+TS+Vite 基础工程（Ant Design Vue），并配置输出到 frontend/public/app-v2（不安装依赖、不改后端）。</goal>
      <steps>
        <s>在仓库根目录新建 frontend-vue/ 目录。</s>
        <s>准备 vite.config.ts（base: "/app-v2/"；build.outDir: "../frontend/public/app-v2"）。</s>
        <s>准备 package.json（scripts、依赖声明：Element Plus + VueUse，仅作文档说明，实际安装需用户同意）。</s>
        <s>准备 src/main.ts、src/App.vue、src/router/index.ts、src/stores/index.ts（空骨架）。</s>
        <s>在 public/ 下放置 index.html（入口路径适配 base）。</s>
        <s>在 docs/tasks/log 中记录产出说明（或在 progress.xml 的 log 中记录）。</s>
      </steps>
      <artifacts>
        <a>frontend-vue/vite.config.ts</a>
        <a>frontend-vue/package.json（仅文档化模板）</a>
        <a>frontend-vue/src/main.ts</a>
        <a>frontend-vue/src/App.vue</a>
        <a>frontend-vue/src/router/index.ts</a>
        <a>frontend-vue/src/stores/index.ts</a>
        <a>frontend-vue/public/index.html</a>
      </artifacts>
      <progress-update>
        <action>将 current 从 setup-structure 移到 next 的 setup-routing；在 completed 追加 <t id="setup-structure"/>；在 log 追加一条 entry，列出生成的文件清单与关键配置。</action>
      </progress-update>
    </task>

    <task id="setup-routing">
      <goal>建立基础路由（/sign-in、/devices、/devices/:id、/updates、/files），并配置守卫占位。</goal>
      <steps>
        <s>定义路由与占位页面组件（空页面）。</s>
        <s>定义路由守卫骨架：检查 token 存在性（实现于后续 auth）。</s>
      </steps>
      <artifacts>
        <a>frontend-vue/src/router/routes.ts</a>
        <a>frontend-vue/src/views/SignIn.vue</a>
        <a>frontend-vue/src/views/Devices.vue</a>
        <a>frontend-vue/src/views/DeviceDetail.vue</a>
        <a>frontend-vue/src/views/Updates.vue</a>
        <a>frontend-vue/src/views/Files.vue</a>
      </artifacts>
      <progress-update>
        <action>完成后，更新 progress.xml：completed 追加 <t id="setup-routing"/>；current 指向 setup-state。</action>
      </progress-update>
    </task>

    <task id="setup-state">
      <goal>初始化 Pinia 与基础类型映射，建立实体仓库（Nodes/Points/Index）。</goal>
      <steps>
        <s>创建 stores/index.ts 挂载 Pinia；创建 stores/nodes.ts 管理 Node/Point 与索引。</s>
        <s>参照 docs/tasks/vue_migration.types.xml 定义 TS 接口，不臆测未定义字段。</s>
        <s>在 main.ts 中注册 Pinia；提供基本选择器（按类型/标签/搜索）。</s>
      </steps>
      <artifacts>
        <a>frontend-vue/src/stores/index.ts</a>
        <a>frontend-vue/src/stores/nodes.ts</a>
        <a>frontend-vue/src/types/index.ts</a>
      </artifacts>
      <progress-update>
        <action>完成后，completed 追加 <t id="setup-state"/>；current 指向 setup-axios；log 记录导出的 store API。</action>
      </progress-update>
    </task>

    <task id="setup-axios">
      <goal>封装 Axios 客户端（baseURL、拦截器注入 Bearer、统一错误处理）。</goal>
      <steps>
        <s>创建 services/http.ts：读取 token（localStorage），注入 Authorization。</s>
        <s>统一处理 401：抛出特定错误码供路由守卫使用；对网络错误给出可读信息。</s>
      </steps>
      <artifacts>
        <a>frontend-vue/src/services/http.ts</a>
      </artifacts>
      <progress-update>
        <action>完成后，completed 追加 <t id="setup-axios"/>；current 指向 auth-page。</action>
      </progress-update>
    </task>

    <task id="auth-page">
      <goal>登录页对接 /v1/auth，持久化 token。</goal>
      <steps>
        <s>创建 services/auth.ts：login(email, password) → POST /v1/auth。</s>
        <s>SignIn.vue 表单提交后保存 token 到 localStorage，更新 store。</s>
      </steps>
      <artifacts>
        <a>frontend-vue/src/services/auth.ts</a>
        <a>frontend-vue/src/views/SignIn.vue</a>
      </artifacts>
      <progress-update>
        <action>完成后，completed 追加 <t id="auth-page"/>；current 指向 auth-guard；log 记录模拟登录验证。</action>
      </progress-update>
    </task>

    <task id="auth-guard">
      <goal>配置路由守卫：无 token 跳转 /sign-in；有 token 放行。</goal>
      <steps>
        <s>router.beforeEach：检测本地 token；对公开路由（/sign-in）放行。</s>
        <s>401 错误时清理 token 并重定向登录。</s>
      </steps>
      <artifacts>
        <a>frontend-vue/src/router/guard.ts</a>
      </artifacts>
      <progress-update>
        <action>完成后，completed 追加 <t id="auth-guard"/>；current 指向 devices-list。</action>
      </progress-update>
    </task>

    <task id="devices-list">
      <goal>设备列表：搜索/筛选/分页，展示关键字段。</goal>
      <steps>
        <s>services/nodes.ts：listNodes() → GET /v1/nodes；归一化入 store。</s>
        <s>Devices.vue：AntD Table + 搜索/筛选；支持分页或虚拟滚动。</s>
      </steps>
      <artifacts>
        <a>frontend-vue/src/services/nodes.ts</a>
        <a>frontend-vue/src/views/Devices.vue</a>
      </artifacts>
      <progress-update>
        <action>完成后，completed 追加 <t id="devices-list"/>；current 指向 devices-detail；log 记录数据量与性能。</action>
      </progress-update>
    </task>

    <task id="devices-detail">
      <goal>设备详情：点位表格读写，提交 /v1/nodes/:id/points。</goal>
      <steps>
        <s>components/PointsTable.vue：可编辑单元格（数值/文本），本地去抖合并。</s>
        <s>services/nodes.ts：postPoints(id, points)。失败回滚并提示。</s>
      </steps>
      <artifacts>
        <a>frontend-vue/src/views/DeviceDetail.vue</a>
        <a>frontend-vue/src/components/PointsTable.vue</a>
      </artifacts>
      <progress-update>
        <action>完成后，completed 追加 <t id="devices-detail"/>；current 指向 devices-mutate。</action>
      </progress-update>
    </task>

    <task id="devices-mutate">
      <goal>节点操作：移动/复制/删除。</goal>
      <steps>
        <s>调用 /v1/nodes/:id/parents（POST move、PUT copy）与 DELETE /v1/nodes/:id。</s>
        <s>提供批量操作与反馈。</s>
      </steps>
      <artifacts>
        <a>frontend-vue/src/components/NodeActions.ts</a>
      </artifacts>
      <progress-update>
        <action>完成后，completed 追加 <t id="devices-mutate"/>；current 指向 updates。</action>
      </progress-update>
    </task>

    <task id="updates">
      <goal>更新管理：展示/控制 Update Client 相关点位（进度、错误、操作）。</goal>
      <steps>
        <s>映射常用更新点位（参见 Point 常量），渲染列表与详情。</s>
        <s>支持触发下载/更新、查看进度与错误。</s>
      </steps>
      <artifacts>
        <a>frontend-vue/src/views/Updates.vue</a>
      </artifacts>
      <progress-update>
        <action>完成后，completed 追加 <t id="updates"/>；current 指向 files。</action>
      </progress-update>
    </task>

    <task id="files">
      <goal>文件管理：上传与分发（基于节点/点位）。</goal>
      <steps>
        <s>支持选择目标设备/分组，将文件路径/哈希等写入相应点位。</s>
        <s>展示文件状态与错误。</s>
      </steps>
      <artifacts>
        <a>frontend-vue/src/views/Files.vue</a>
      </artifacts>
      <progress-update>
        <action>完成后，completed 追加 <t id="files"/>；current 指向 nats-client。</action>
      </progress-update>
    </task>

    <task id="nats-client">
      <goal>实时：封装 nats.ws 客户端，订阅节点/点位主题并分发到 store。</goal>
      <steps>
        <s>services/nats.ts：connect()、subscribe(subjects)、emit 更新。</s>
        <s>与 store 解耦，通过事件/回调更新；记录断线与状态。</s>
      </steps>
      <artifacts>
        <a>frontend-vue/src/services/nats.ts</a>
      </artifacts>
      <progress-update>
        <action>完成后，completed 追加 <t id="nats-client"/>；current 指向 reconnect。</action>
      </progress-update>
    </task>

    <task id="reconnect">
      <goal>断线重连：指数退避；重连后触发一次全量校准（GET /v1/nodes）。</goal>
      <steps>
        <s>封装重连策略；在 onReconnect 中触发 listNodes() 并与 store 合并。</s>
      </steps>
      <artifacts>
        <a>frontend-vue/src/services/nats.ts</a>
        <a>frontend-vue/src/services/nodes.ts</a>
      </artifacts>
      <progress-update>
        <action>完成后，completed 追加 <t id="reconnect"/>；current 指向 polling。</action>
      </progress-update>
    </task>

    <task id="polling">
      <goal>轮询兜底：WS 不可用时 4s 轮询，合并变化。</goal>
      <steps>
        <s>在合适的全局入口启用 setInterval 调用 listNodes()；去抖合并。</s>
      </steps>
      <artifacts>
        <a>frontend-vue/src/services/polling.ts</a>
      </artifacts>
      <progress-update>
        <action>完成后，completed 追加 <t id="polling"/>；current 指向 modules（根据 tasks.xml 选择下一个模块，如 rules/metrics/protocols）。</action>
      </progress-update>
    </task>
  </task-templates>

    <task id="rules">
      <goal>规则：条件/动作配置与列表（首版以表单为主）。</goal>
      <steps>
        <s>映射规则相关点位（条件类型、操作类型、阈值/时间段等）。</s>
        <s>提供新增/编辑/删除规则，落点到节点/点位。</s>
      </steps>
      <artifacts>
        <a>frontend-vue/src/views/Rules.vue</a>
      </artifacts>
      <progress-update>
        <action>完成后，completed 追加 <t id="rules"/>；根据 tasks.xml 选择下一个（如 metrics 或某协议）。</action>
      </progress-update>
    </task>

    <task id="metrics">
      <goal>指标：实时卡片/图表展示。</goal>
      <steps>
        <s>订阅指标相关主题或轮询，绘制图表（ECharts）。</s>
        <s>提供时间范围/采样率（若有历史则增强）。</s>
      </steps>
      <artifacts>
        <a>frontend-vue/src/views/Metrics.vue</a>
      </artifacts>
      <progress-update>
        <action>完成后，completed 追加 <t id="metrics"/>；继续协议类模块。</action>
      </progress-update>
    </task>

    <task id="protocols">
      <goal>协议类模块：Modbus、CAN、NetworkManager、OneWire、Shelly 等。</goal>
      <steps>
        <s>为每类 Client 建立配置与诊断页（优先 1-2 个，如 Modbus、NetworkManager）。</s>
        <s>表格/表单模式统一，减少专业门槛；重用通用组件。</s>
      </steps>
      <artifacts>
        <a>frontend-vue/src/views/protocols/Modbus.vue</a>
        <a>frontend-vue/src/views/protocols/NetworkManager.vue</a>
      </artifacts>
      <progress-update>
        <action>完成后，completed 追加 <t id="protocols"/>；进入集成与验收阶段。</action>
      </progress-update>
    </task>

    <task id="acceptance">
      <goal>阶段验收：核心功能闭环与性能体验达标。</goal>
      <steps>
        <s>按 plan.xml 的 acceptance 条目逐项验证（登录、设备、点位、变更、WS/轮询）。</s>
        <s>补充 e2e 测试与用户手册/开发文档。</s>
      </steps>
      <artifacts>
        <a>tests/e2e/*（文档化说明）</a>
        <a>docs/user/*（新增/修订）</a>
      </artifacts>
      <progress-update>
        <action>完成后，completed 追加 <t id="acceptance"/>；准备移除旧 UI 的计划（单独任务）。</action>
      </progress-update>
    </task>
  </task-templates>
</wakeup-guide>

