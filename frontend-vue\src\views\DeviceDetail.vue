<template>
  <div class="device-detail-container">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside width="200px">
        <el-menu
          default-active="/devices"
          router
          class="sidebar-menu"
        >
          <el-menu-item index="/devices">
            <el-icon><Monitor /></el-icon>
            <span>设备管理</span>
          </el-menu-item>
          <el-menu-item index="/updates">
            <el-icon><Download /></el-icon>
            <span>软件更新</span>
          </el-menu-item>
          <el-menu-item index="/files">
            <el-icon><Folder /></el-icon>
            <span>文件管理</span>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 主内容区 -->
      <el-container>
        <!-- 顶部栏 -->
        <el-header>
          <div class="header-content">
            <div class="header-left">
              <el-button :icon="ArrowLeft" @click="goBack">返回</el-button>
              <h2>设备详情 - {{ deviceId }}</h2>
            </div>
          </div>
        </el-header>

        <!-- 主内容 -->
        <el-main>
          <div v-if="device">
            <!-- 设备概览 -->
            <el-card style="margin-bottom: 16px;">
              <template #header>
                <span>设备概览</span>
              </template>
              <el-descriptions :column="3" border>
                <el-descriptions-item label="设备ID">{{ device.id }}</el-descriptions-item>
                <el-descriptions-item label="设备类型">{{ device.type }}</el-descriptions-item>
                <el-descriptions-item label="描述">{{ getNodeDescription(device) }}</el-descriptions-item>
              </el-descriptions>
            </el-card>

            <!-- 点位数据 -->
            <el-card>
              <template #header>
                <div class="card-header">
                  <span>点位数据</span>
                  <el-button type="primary" :icon="Refresh" @click="refreshData">刷新</el-button>
                </div>
              </template>

              <el-table :data="device.points" style="width: 100%">
                <el-table-column prop="key" label="键名" width="200" />
                <el-table-column prop="typ" label="类型" width="120" />
                <el-table-column label="值" width="150">
                  <template #default="scope">
                    <span v-if="scope.row.typ === 'value'">{{ scope.row.value }}</span>
                    <span v-else>{{ scope.row.text }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="时间">
                  <template #default="scope">
                    {{ formatTime(scope.row.time) }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="120">
                  <template #default="scope">
                    <el-button 
                      type="primary" 
                      size="small"
                      @click="editPoint(scope.row)"
                    >
                      编辑
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </div>

          <div v-else>
            <el-empty description="设备不存在" />
          </div>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  Monitor, Download, Folder, ArrowLeft, Refresh 
} from '@element-plus/icons-vue'
import { useNodesStore } from '@/stores'
import type { NodeEdge, Point } from '@/types'

const route = useRoute()
const router = useRouter()
const nodesStore = useNodesStore()

const deviceId = computed(() => route.params.id as string)
const device = computed(() => nodesStore.getNode(deviceId.value))

// 方法
function goBack() {
  router.push('/devices')
}

function getNodeDescription(node: NodeEdge) {
  const descPoint = node.points.find(p => p.key === 'description')
  return descPoint?.text || '无描述'
}

function formatTime(time: string | number) {
  const date = new Date(typeof time === 'string' ? parseInt(time) : time)
  return date.toLocaleString()
}

function refreshData() {
  // TODO: 刷新设备数据
  ElMessage.success('数据已刷新')
}

function editPoint(point: Point) {
  // TODO: 编辑点位
  ElMessage.info(`编辑点位: ${point.key}`)
}

// 生命周期
onMounted(() => {
  if (!device.value) {
    // TODO: 从 API 加载设备详情
    ElMessage.warning('设备不存在，请先访问设备列表')
    router.push('/devices')
  }
})
</script>

<style scoped>
.device-detail-container {
  height: 100vh;
}

.sidebar-menu {
  height: 100vh;
  border-right: 1px solid #e6e6e6;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 16px;
  border-bottom: 1px solid #e6e6e6;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-left h2 {
  margin: 0;
  color: #303133;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
