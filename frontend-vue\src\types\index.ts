// 基于 docs/tasks/vue_migration.types.xml 的类型定义

// 认证相关
export interface User {
  token: string
  email: string
}

// 标准响应
export interface StandardResponse {
  success: boolean
  error?: string
  id?: string
}

// 点位数据
export interface Point {
  typ: string
  key: string
  time: string | number
  value: number
  text: string
  tombstone: number
}

// 节点边缘数据
export interface NodeEdge {
  id: string
  type: string
  parent?: string
  points: Point[]
  edgePoints?: Point[]
  children?: string[]
}

// 节点操作
export interface NodeMove {
  id: string
  oldParent: string
  newParent: string
}

export interface NodeCopy {
  id: string
  newParent: string
  duplicate: boolean
}

export interface NodeDelete {
  parent: string
}

// 常用点位类型常量（基于 Elm 前端）
export const PointTypes = {
  DESCRIPTION: 'description',
  VERSION_OS: 'versionOS',
  VALUE: 'value',
  TEXT: 'text',
  COUNT: 'count',
  MIN: 'min',
  MAX: 'max',
  // 软件更新相关
  SW_UPDATE_RUNNING: 'swUpdateRunning',
  SW_UPDATE_ERROR: 'swUpdateError',
  SW_UPDATE_PERCENT: 'swUpdatePercent',
  // 更多类型待补充...
} as const

// 节点类型常量
export const NodeTypes = {
  USER: 'user',
  JWT: 'jwt',
  DEVICE: 'device',
  GROUP: 'group',
  RULE: 'rule',
  MODBUS: 'modbus',
  NETWORK_MANAGER: 'networkManager',
  UPDATE: 'update',
  FILE: 'file',
  METRICS: 'metrics',
  MESSAGE: 'message',
} as const
